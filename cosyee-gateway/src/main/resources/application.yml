server:
  port: 9000

spring:
  application:
    name: cosyee-gateway

  cloud:
    gateway:
      routes:
        # 对话服务 - 强化记忆机制核心接口
        - id: conversation-api
          uri: http://localhost:9001
          predicates:
            - Path=/api/conversations/**
          filters:
            - AddRequestHeader=X-Gateway-Source, cosyee-gateway

        # 对话服务健康检查
        - id: conversation-health
          uri: http://localhost:9001
          predicates:
            - Path=/api/health
          filters:
            - AddRequestHeader=X-Gateway-Source, cosyee-gateway

      # 全局 CORS 配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            allowedHeaders: "*"
            allowCredentials: false
            maxAge: 3600

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    org.springframework.web: DEBUG
    com.cosyee: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
