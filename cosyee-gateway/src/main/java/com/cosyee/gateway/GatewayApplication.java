package com.cosyee.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Cosyee Gateway 应用启动类
 * 路由配置完全在 application.yml 中定义
 */
@SpringBootApplication
@RestController
public class GatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
    }

    /**
     * Gateway 健康检查端点
     */
    @GetMapping("/")
    public Map<String, Object> home() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "Cosyee Gateway");
        response.put("version", "1.0.0");
        response.put("status", "running");
        response.put("timestamp", LocalDateTime.now());
        response.put("description", "Spring Cloud Gateway for Cosyee AI Chat System");
        return response;
    }

    /**
     * Gateway 信息端点
     */
    @GetMapping("/info")
    public Map<String, Object> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("gateway", "Cosyee Gateway");
        response.put("version", "1.0.0");
        response.put("routes", new String[]{
            "/api/conversations/** -> cosyee-aichat-conversation:9001",
            "/api/health -> cosyee-aichat-conversation:9001"
        });
        response.put("features", new String[]{
            "Load Balancing",
            "CORS Support", 
            "Request/Response Filtering",
            "Health Monitoring"
        });
        return response;
    }
}
