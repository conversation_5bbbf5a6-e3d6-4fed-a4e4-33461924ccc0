#!/usr/bin/env python3
"""
简化的MCP服务器 - 直接调用HTTP接口
支持会话隔离和用户身份管理
"""

import asyncio
import json
import sys
import uuid
from typing import Any, Dict, List, Optional
import aiohttp
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# 服务配置
CONVERSATION_SERVICE_URL = "http://localhost:9001"
GATEWAY_URL = "http://localhost:9000"
USE_GATEWAY = True

# 选择服务URL
BASE_URL = GATEWAY_URL if USE_GATEWAY else CONVERSATION_SERVICE_URL

app = Server("cosyee-aichat-simple")

# 全局会话状态管理
class SessionManager:
    def __init__(self):
        self.current_session_token: Optional[str] = None
        self.current_user_id: Optional[str] = None

    def set_session(self, session_token: str, user_id: str):
        """设置当前会话"""
        self.current_session_token = session_token
        self.current_user_id = user_id

    def get_session_info(self) -> tuple[Optional[str], Optional[str]]:
        """获取当前会话信息"""
        return self.current_session_token, self.current_user_id

    def generate_session_token(self) -> str:
        """生成新的会话令牌"""
        return str(uuid.uuid4())

# 全局会话管理器
session_manager = SessionManager()

@app.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用工具"""
    return [
        Tool(
            name="register_user",
            description="注册新用户 - 创建持久用户身份",
            inputSchema={
                "type": "object",
                "properties": {
                    "username": {
                        "type": "string",
                        "description": "用户名（唯一标识）"
                    },
                    "display_name": {
                        "type": "string",
                        "description": "显示名称"
                    },
                    "email": {
                        "type": "string",
                        "description": "邮箱地址（可选）"
                    },
                    "user_type": {
                        "type": "string",
                        "description": "用户类型",
                        "enum": ["individual", "business", "developer"],
                        "default": "individual"
                    }
                },
                "required": ["username"]
            }
        ),
        Tool(
            name="start_conversation",
            description="开始对话 - 获取强化记忆指令",
            inputSchema={
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "对话主题"
                    },
                    "user_id": {
                        "type": "string",
                        "description": "已注册的用户ID，如果不提供将创建匿名会话"
                    },
                    "session_token": {
                        "type": "string",
                        "description": "会话令牌，如果不提供将自动生成"
                    }
                },
                "required": ["topic"]
            }
        ),
        Tool(
            name="query_conversations",
            description="查询历史对话 - 获取上下文信息（自动使用当前会话的用户身份）",
            inputSchema={
                "type": "object",
                "properties": {
                    "keywords": {
                        "type": "string",
                        "description": "搜索关键词"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回结果数量",
                        "default": 5
                    }
                },
                "required": ["keywords"]
            }
        ),
        Tool(
            name="save_conversation",
            description="保存对话记录（自动使用当前会话的用户身份）",
            inputSchema={
                "type": "object",
                "properties": {
                    "userQuestion": {
                        "type": "string",
                        "description": "用户问题"
                    },
                    "aiResponse": {
                        "type": "string",
                        "description": "AI回答"
                    },
                    "topic": {
                        "type": "string",
                        "description": "对话主题"
                    },
                    "importance": {
                        "type": "string",
                        "description": "重要性级别",
                        "enum": ["LOW", "MEDIUM", "HIGH"],
                        "default": "MEDIUM"
                    },
                    "tags": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "标签列表",
                        "default": []
                    }
                },
                "required": ["userQuestion", "aiResponse", "topic"]
            }
        ),
        Tool(
            name="get_user_info",
            description="获取当前用户信息",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    ]

@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """处理工具调用"""
    
    try:
        async with aiohttp.ClientSession() as session:

            if name == "register_user":
                # 用户注册逻辑
                data = {
                    "username": arguments.get("username"),
                    "display_name": arguments.get("display_name", arguments.get("username")),
                    "email": arguments.get("email"),
                    "user_type": arguments.get("user_type", "individual")
                }

                async with session.post(f"{BASE_URL}/api/users/register", json=data) as resp:
                    if resp.status == 200:
                        result = await resp.json()

                        register_info = f"""✅ 用户注册成功

👤 用户信息:
- 用户名: {result.get('username')}
- 显示名: {result.get('display_name')}
- 用户ID: {result.get('user_id')}
- 用户类型: {result.get('user_type')}
- 注册时间: {result.get('created_at')}

🎯 现在您可以使用 start_conversation 开始对话，记得传入您的 user_id: {result.get('user_id')}"""

                        return [TextContent(type="text", text=register_info)]
                    elif resp.status == 409:
                        return [TextContent(type="text", text="❌ 用户名已存在，请选择其他用户名")]
                    else:
                        error_text = await resp.text()
                        return [TextContent(type="text", text=f"❌ 注册失败: {error_text}")]

            elif name == "start_conversation":
                # 生成或使用提供的会话令牌和用户ID
                session_token = arguments.get("session_token") or session_manager.generate_session_token()
                user_id = arguments.get("user_id") or f"user_{session_token[:8]}"

                # 保存会话信息到管理器
                session_manager.set_session(session_token, user_id)

                # 调用开始对话接口
                data = {
                    "topic": arguments.get("topic"),
                    "topic_category": "general",  # 简化版本，固定为general
                    "user_id": user_id,
                    "session_token": session_token
                }

                async with session.post(f"{BASE_URL}/api/conversations/start", json=data) as resp:
                    if resp.status == 200:
                        result = await resp.json()

                        # 生成简化的强化记忆指令
                        memory_instruction = f"""🧠 强化记忆机制激活

💡 当前会话信息:
- 主题: {result.get('topic', '未知')}
- 会话ID: {result.get('session_id', '未知')}
- 用户: {user_id}
- 会话令牌: {session_token}

📝 行为指令:
1. 认真回答用户关于 "{arguments.get('topic')}" 的问题
2. 回答完毕后调用 save_conversation 工具保存对话
3. 如需了解历史上下文，可调用 query_conversations 工具
4. 保持专业和准确性
5. 所有操作将自动使用当前会话的用户身份，确保数据隔离

🎯 现在开始回答用户问题..."""

                        return [TextContent(type="text", text=memory_instruction)]
                    else:
                        error_text = await resp.text()
                        return [TextContent(type="text", text=f"❌ 启动对话失败: {error_text}")]
            
            elif name == "query_conversations":
                # 获取当前会话信息
                session_token, user_id = session_manager.get_session_info()

                if not session_token or not user_id:
                    return [TextContent(type="text", text="❌ 请先调用 start_conversation 初始化会话")]

                # 调用查询对话接口，使用当前会话的用户身份
                params = {
                    "keywords": arguments.get("keywords"),
                    "user_id": user_id,
                    "session_token": session_token,
                    "limit": arguments.get("limit", 5)
                }

                async with session.get(f"{BASE_URL}/api/conversations/query", params=params) as resp:
                    if resp.status == 200:
                        result = await resp.json()
                        conversations = result.get("conversations", [])
                        
                        if not conversations:
                            context_info = f"""📚 历史上下文查询结果:

🔍 搜索关键词: {arguments.get('keywords')}
📊 找到记录: 0条
👤 当前用户: {user_id}
🔐 会话令牌: {session_token[:8]}...

💡 这是关于 "{arguments.get('keywords')}" 的首次对话
建议从基础概念开始，提供清晰易懂的解释。

⚠️ 提醒: 回答完毕后请调用 save_conversation 工具保存对话"""
                        else:
                            # 格式化历史记录
                            history_text = ""
                            for i, conv in enumerate(conversations[:3], 1):
                                history_text += f"{i}. {conv.get('topic', '未知主题')} ({conv.get('createdAt', '未知时间')})\n"
                                history_text += f"   问题: {conv.get('userQuestion', '')[:100]}...\n"
                                history_text += f"   回答: {conv.get('aiResponse', '')[:150]}...\n\n"
                            
                            context_info = f"""📚 历史上下文查询结果:

🔍 搜索关键词: {arguments.get('keywords')}
📊 找到记录: {len(conversations)}条
👤 当前用户: {user_id}
🔐 会话令牌: {session_token[:8]}...

📝 相关历史对话:
{history_text}

💡 基于历史记录，用户对此主题有一定了解
建议在之前讨论的基础上深入展开，避免重复基础内容。

⚠️ 提醒: 回答完毕后请调用 save_conversation 工具保存对话"""
                        
                        return [TextContent(type="text", text=context_info)]
                    else:
                        error_text = await resp.text()
                        return [TextContent(type="text", text=f"❌ 查询历史失败: {error_text}")]
            
            elif name == "save_conversation":
                # 获取当前会话信息
                session_token, user_id = session_manager.get_session_info()

                if not session_token or not user_id:
                    return [TextContent(type="text", text="❌ 请先调用 start_conversation 初始化会话")]

                # 调用保存对话接口，使用当前会话的用户身份
                data = {
                    "userQuestion": arguments.get("userQuestion"),
                    "aiResponse": arguments.get("aiResponse"),
                    "topic": arguments.get("topic"),
                    "importance": arguments.get("importance", "MEDIUM"),
                    "tags": arguments.get("tags", []),
                    "userId": user_id,
                    "sessionToken": session_token
                }

                async with session.post(f"{BASE_URL}/api/conversations/end", json=data) as resp:
                    if resp.status == 200:
                        result = await resp.json()
                        
                        save_info = f"""✅ 对话已成功保存

📊 保存详情:
- 记录ID: {result.get('conversation_id', '未知')}
- 主题: {arguments.get('topic')}
- 重要性: {arguments.get('importance', 'MEDIUM')}
- 标签: {', '.join(arguments.get('tags', [])) or '无'}
👤 用户: {user_id}
🔐 会话令牌: {session_token[:8]}...

🧠 记忆分析:
- 用户风格: {result.get('memory_analysis', {}).get('user_style', '未分析')}
- 关键点: {', '.join(result.get('memory_analysis', {}).get('key_points', []))}

🎯 对话记录已加入您的个人知识库，下次相关讨论时会自动参考。"""
                        
                        return [TextContent(type="text", text=save_info)]
                    else:
                        error_text = await resp.text()
                        return [TextContent(type="text", text=f"❌ 保存对话失败: {error_text}")]
            
            else:
                return [TextContent(type="text", text=f"❌ 未知工具: {name}")]
                
    except Exception as e:
        return [TextContent(type="text", text=f"❌ 工具调用失败: {str(e)}")]

async def main():
    """启动简化MCP服务器"""
    print("🚀 启动简化版 Cosyee AI Chat MCP 服务器...")
    print(f"🔗 连接到: {BASE_URL}")
    print("🎯 可用工具: start_conversation, query_conversations, save_conversation")
    print("-" * 50)
    
    async with stdio_server() as (read_stream, write_stream):
        await app.run(read_stream, write_stream, app.create_initialization_options())

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 MCP 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
