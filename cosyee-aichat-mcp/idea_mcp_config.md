# 🧠 IntelliJ IDEA MCP 配置指南

## 📋 配置步骤

### 1. 打开MCP设置
1. 打开 `Settings` (Cmd+, 或 Ctrl+,)
2. 导航到 `Tools > AI Assistant > Model Context Protocol (MCP)`

### 2. 添加MCP服务器
1. 点击 `Add` 按钮 (或按 Alt+Insert)
2. 填写配置信息：

```
Name: cosyee-aichat
Command: /Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/venv/bin/python
Arguments: /Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/simple_mcp.py
Working directory: /Users/<USER>/cosyee-cloud/cosyee-aichat-mcp
Environment Variables: (留空)
```

### 3. 保存配置
1. 点击 `OK` 保存服务器配置
2. 在 `Level` 列选择 `Global` 或 `Project`
3. 点击 `Apply` 应用更改

### 4. 验证连接
- 在 `Status` 列查看连接状态
- 状态应显示为 `Connected` 或绿色图标
- 点击状态图标可查看可用工具列表

## 🎯 使用方式

### 在AI Assistant中使用
1. 打开AI Assistant聊天窗口
2. 确保启用了 `Codebase mode`
3. 使用 `/` 命令调用MCP工具：

```
/start_conversation 主题是 "Spring Boot开发"
/query_conversations 搜索关键词 "Spring"
/save_conversation 保存这次对话
```

### 可用命令
- `/start_conversation` - 开始对话，获取强化记忆指令
- `/query_conversations` - 查询历史对话，获取上下文信息
- `/save_conversation` - 保存对话记录

## 🔧 故障排除

### 连接失败
1. 检查Python路径是否正确
2. 确保虚拟环境已激活
3. 验证simple_mcp.py文件路径
4. 检查后端服务是否运行 (localhost:9001)

### 工具不可用
1. 确保MCP服务器状态为 `Connected`
2. 重启IDEA并重新连接
3. 检查AI Assistant是否启用了Codebase模式

### 权限问题
1. 确保Python可执行文件有执行权限
2. 检查工作目录的读写权限

## 📝 JSON配置 (可选)

如果喜欢JSON配置方式，可以选择 `As JSON` 选项：

```json
{
  "command": "/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/venv/bin/python",
  "args": ["/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/simple_mcp.py"],
  "cwd": "/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp"
}
```

## 🎉 开始使用

配置完成后，您就可以在IDEA的AI Assistant中享受强化记忆功能了！

每次对话时，AI都会：
1. 🧠 获得强化记忆指令
2. 📚 恢复相关的历史上下文
3. 💾 保存对话记录供未来参考
4. 🔄 持续优化对话质量
