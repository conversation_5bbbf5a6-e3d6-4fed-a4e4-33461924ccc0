# 🧠 Cosyee AI Chat MCP Server

简洁的MCP服务器，为AI对话提供强化记忆功能。

## 🎯 功能

- **start_conversation** - 开始对话，获取强化记忆指令
- **query_conversations** - 查询历史对话，获取上下文信息
- **save_conversation** - 保存对话记录

## 🚀 使用

1. **启动后端服务**：
   ```bash
   cd ../cosyee-aichat-conversation && mvn spring-boot:run
   ```

2. **配置MCP**：

   ### IntelliJ IDEA / JetBrains IDEs
   1. 打开 `Settings > Tools > AI Assistant > Model Context Protocol (MCP)`
   2. 点击 `Add` 按钮
   3. 配置参数：
      - **Name**: `cosyee-aichat`
      - **Command**: `/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/venv/bin/python`
      - **Arguments**: `/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/simple_mcp.py`
      - **Working directory**: `/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp`

   ### VSCode
   在 `.vscode/mcp.json` 中添加：
   ```json
   {
     "servers": {
       "cosyee-aichat": {
         "type": "stdio",
         "command": "/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/venv/bin/python",
         "args": ["/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/simple_mcp.py"]
       }
     }
   }
   ```

   ### Claude Desktop
   在 `claude_desktop_config.json` 中添加：
   ```json
   {
     "mcpServers": {
       "cosyee-aichat": {
         "command": "/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/venv/bin/python",
         "args": ["/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp/simple_mcp.py"],
         "cwd": "/Users/<USER>/cosyee-cloud/cosyee-aichat-mcp"
       }
     }
   }
   ```

3. **在AI对话中使用**：

   ### IntelliJ IDEA
   在AI Assistant聊天中输入：
   ```
   /start_conversation 主题是 "Python学习"
   ```

   ### VSCode / Claude Desktop
   ```
   请调用 start_conversation 工具，主题是 "Python学习"
   ```

## 📋 标准流程

```
start_conversation → query_conversations → [AI回答] → save_conversation
```

就这么简单！🎉
