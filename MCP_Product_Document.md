# Cosyee AI Chat MCP 产品文档

## 1. 产品概述

### 1.1 产品定位
Cosyee AI Chat MCP 是一个基于 Model Context Protocol (MCP) 的AI对话记忆增强系统，旨在为用户提供跨设备、跨时间的连续对话体验。

### 1.2 核心价值
- **记忆连续性**：AI能够记住并参考历史对话内容
- **跨设备同步**：用户可在不同设备间无缝切换对话
- **个性化体验**：基于用户历史优化回答质量
- **知识积累**：构建个人专属的AI知识库

### 1.3 目标用户
- 开发者：需要技术问题的连续讨论
- 企业用户：需要项目相关的持续沟通
- 个人用户：希望获得个性化的AI助手体验

## 2. 用户管理模块

### 2.1 用户注册
**注册方式**：
- 通过邮箱注册，系统自动生成唯一用户ID
- 用户名必须唯一，作为用户身份标识
- 最小化用户信息收集

**注册流程**：
```
用户输入邮箱 → 系统验证邮箱格式 → 生成用户ID和用户名 → 创建用户记录
```

**数据结构**：
```json
{
  "user_id": "usr_abc123",
  "username": "user_abc123", 
  "email": "<EMAIL>",
  "display_name": "user",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 2.2 用户认证
- 基于用户名进行身份校验
- 校验失败则中断对话并提示
- 支持跨平台身份同步

## 3. 会话管理模块

### 3.1 指令集设计
```
/cnvst [username] [topic]  - 开始会话（必须携带用户名）
/cnvqy [keywords]          - 查询会话历史
/cnvsv                     - 保存当前对话
```

### 3.2 会话开始流程

#### 3.2.1 用户校验
1. 检查用户表中username是否存在
2. 不存在则提示"用户不存在"并中断对话
3. 存在则继续后续流程

#### 3.2.2 Topic匹配逻辑
1. 查询用户所有topic并去重，取最近的5个topic
2. 如果当前topic与历史topic完全相同，自动延续该topic
3. 如果不同，展示历史topic列表供用户选择

#### 3.2.3 SessionID管理
- **自动延续**：使用该topic最新记录的sessionid
- **用户选择延续**：使用选中topic的最新sessionid  
- **新建对话**：生成新的随机sessionid

#### 3.2.4 用户交互
**自动匹配示例**：
```
用户：/cnvst john "Python学习"
系统：🔄 发现相同主题的历史对话，自动延续会话...
      会话信息：Python学习 (session_abc123)
      ✅ 开始对话...
```

**用户选择示例**：
```
用户：/cnvst john "React开发"
系统：📋 发现您的历史对话主题：
      1. Python学习 (2024-01-15)
      2. MCP建设 (2024-01-14) 
      3. Spring Boot (2024-01-13)
      4. 数据库设计 (2024-01-12)
      5. 前端开发 (2024-01-11)
      
      当前主题：React开发
      请选择数字(1-5)延续对应主题，或输入"new"创建新对话：

用户：new
系统：✅ 创建新对话 (session_xyz789)
      开始对话...
```

### 3.3 会话查询功能
- 基于关键词的智能搜索
- 用户隔离的历史记录查询
- 上下文相关性分析
- 返回格式化的历史对话摘要

### 3.4 会话保存功能
- 结构化对话数据存储
- 重要性级别标记（LOW/MEDIUM/HIGH）
- 标签系统支持
- 记忆分析与用户风格识别

## 4. 数据存储设计

### 4.1 用户表 (users)
```sql
CREATE TABLE users (
    user_id VARCHAR(50) PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4.2 对话表 (conversations)
```sql
CREATE TABLE conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(100) NOT NULL,
    username VARCHAR(100) NOT NULL,
    topic VARCHAR(255) NOT NULL,
    user_question TEXT,
    ai_response TEXT,
    importance ENUM('LOW', 'MEDIUM', 'HIGH') DEFAULT 'MEDIUM',
    tags JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username_topic (username, topic),
    INDEX idx_session_id (session_id),
    INDEX idx_created_at (created_at)
);
```

## 5. API接口设计

### 5.1 用户注册
```
POST /api/users/register
Content-Type: application/json

{
  "email": "<EMAIL>"
}

Response:
{
  "user_id": "usr_abc123",
  "username": "user_abc123",
  "email": "<EMAIL>",
  "display_name": "user",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 5.2 开始对话
```
POST /api/conversations/start
Content-Type: application/json

{
  "username": "user_abc123",
  "topic": "Python学习",
  "session_token": "optional"
}

Response:
{
  "session_id": "session_abc123",
  "topic": "Python学习",
  "is_continuation": true,
  "message": "发现相同主题的历史对话，自动延续会话..."
}
```

### 5.3 查询对话
```
GET /api/conversations/query?username=user_abc123&keywords=Python&limit=5

Response:
{
  "conversations": [
    {
      "topic": "Python学习",
      "user_question": "如何使用装饰器？",
      "ai_response": "装饰器是Python的一个重要特性...",
      "created_at": "2024-01-15T10:00:00Z"
    }
  ],
  "total": 1
}
```

### 5.4 保存对话
```
POST /api/conversations/save
Content-Type: application/json

{
  "session_id": "session_abc123",
  "username": "user_abc123",
  "topic": "Python学习",
  "user_question": "什么是生成器？",
  "ai_response": "生成器是一种特殊的迭代器...",
  "importance": "MEDIUM",
  "tags": ["Python", "生成器", "迭代器"]
}

Response:
{
  "conversation_id": 123,
  "message": "对话已成功保存",
  "memory_analysis": {
    "user_style": "喜欢深入技术细节",
    "key_points": ["Python基础", "高级特性"]
  }
}
```

## 6. 技术架构

### 6.1 系统架构
- **MCP服务器**：基于Python的MCP协议实现
- **后端服务**：Spring Boot REST API服务
- **数据库**：MySQL/PostgreSQL关系型数据库
- **缓存**：Redis用于会话状态管理

### 6.2 部署架构
```
[客户端] → [MCP服务器] → [API网关] → [后端服务] → [数据库]
                                    ↓
                                 [缓存层]
```

### 6.3 平台支持
- IntelliJ IDEA / JetBrains IDEs
- VSCode
- Claude Desktop
- 其他支持MCP协议的客户端

## 7. 使用流程

### 7.1 标准工作流程
```
用户注册 → 配置MCP → 开始对话(/cnvst) → 查询历史(/cnvqy) → AI回答 → 保存对话(/cnvsv)
```

### 7.2 典型使用场景

**场景1：新用户首次使用**
1. 用户通过邮箱注册获得用户名
2. 配置MCP客户端
3. 使用 `/cnvst username topic` 开始对话
4. 系统创建新会话并开始对话

**场景2：老用户延续对话**
1. 用户使用 `/cnvst username topic` 
2. 系统发现相同topic，自动延续
3. 或展示历史topic供用户选择
4. 基于历史上下文继续对话

## 8. 产品特性

### 8.1 核心特性
- ✅ 跨设备会话同步
- ✅ 智能topic匹配
- ✅ 历史上下文查询
- ✅ 个性化记忆分析
- ✅ 多平台客户端支持

### 8.2 技术特性
- ✅ 基于MCP标准协议
- ✅ RESTful API设计
- ✅ 异步处理优化
- ✅ 数据隔离和安全
- ✅ 可扩展架构设计

## 9. 后续规划

### 9.1 功能扩展
- [ ] 对话导出和分享功能
- [ ] 团队协作和权限管理
- [ ] 多语言支持
- [ ] 语音对话支持
- [ ] 移动端应用

### 9.2 技术优化
- [ ] 性能监控和优化
- [ ] 分布式部署支持
- [ ] 数据备份和恢复
- [ ] 安全加固
- [ ] API限流和防护

---

**文档版本**：v1.0  
**最后更新**：2024-01-30  
**维护团队**：Cosyee开发团队
