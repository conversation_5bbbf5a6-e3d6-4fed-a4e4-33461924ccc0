# 📘 VSCode MCP 集成指南

## 🎯 概述

本文档记录了如何在 VSCode 中配置和使用 MCP (Model Context Protocol) 服务器，基于我们的实际测试经验。

## 🔧 配置方式

### 1. 配置文件位置

VSCode 中的 MCP 配置文件位于项目根目录：
```
.vscode/mcp.json
```

### 2. 配置文件格式

```json
{
  "servers": {
    "服务器名称": {
      "type": "stdio",
      "command": "命令路径",
      "args": ["参数1", "参数2", "..."]
    }
  }
}
```

### 3. 实际配置示例

#### Hello World MCP 服务器配置
```json
{
  "servers": {
    "hello-world": {
      "type": "stdio",
      "command": "/Users/<USER>/cosyee-cloud/mcp-server/venv/bin/python",
      "args": ["/Users/<USER>/cosyee-cloud/mcp-server/hello_world_mcp.py"]
    }
  }
}
```

#### 多个 MCP 服务器配置
```json
{
  "servers": {
    "cosyee-aichat": {
      "type": "stdio",
      "command": "/path/to/python",
      "args": ["/path/to/cosyee-aichat-mcp.py"]
    },
    "filesystem": {
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/allowed/directory"]
    },
    "github": {
      "type": "stdio",
      "command": "uvx",
      "args": ["mcp-server-github", "--github-personal-access-token", "your_token"]
    },
    "database": {
      "type": "stdio",
      "command": "python",
      "args": ["/path/to/database-mcp.py", "--host", "localhost"]
    }
  }
}
```

## 🚀 使用流程

### 1. 启动 MCP 服务器

1. **打开 VSCode**
2. **打开命令面板** (Cmd+Shift+P / Ctrl+Shift+P)
3. **运行命令**: `MCP: Show Installed Servers`
4. **启动服务器**: 在列表中找到配置的服务器并启动

### 2. 在 Copilot Chat 中使用

1. **打开 Copilot Chat**
2. **切换到 Agent 模式** (点击 Agent 按钮)
3. **调用 MCP 工具**:
   ```
   请使用 say_hello 工具向我问好
   请使用 add_numbers 工具计算 15 + 27
   请使用 get_server_info 工具获取服务器信息
   ```

## 🧪 测试验证

### 1. 服务器状态检查

在 VSCode 中可以看到：
- ✅ **服务器名称**: hello-world
- ✅ **协议版本**: 2024-11-05  
- ✅ **发现工具**: 3 个工具
- ✅ **运行状态**: 正常

### 2. 工具调用测试

成功调用示例：
```
用户: 请使用 say_hello 工具向 VSCode 问好
AI: 调用 say_hello 工具...
结果: Hello, VSCode! 🎉 MCP is working!
```

## 📋 配置要点

### 1. 路径配置
- **绝对路径**: 推荐使用绝对路径避免路径问题
- **Python 环境**: 确保使用正确的 Python 环境（如虚拟环境）
- **权限检查**: 确保文件有执行权限

### 2. 服务器类型
- **stdio**: 标准输入输出通信（最常用）
- **sse**: Server-Sent Events（HTTP 流式）
- **http**: HTTP 请求响应

### 3. 常见问题

#### 问题1: 服务器启动失败
```
解决方案:
1. 检查 Python 路径是否正确
2. 检查脚本文件是否存在
3. 检查文件权限
4. 查看 VSCode 输出面板的错误信息
```

#### 问题2: 工具列表为空
```
解决方案:
1. 确保 MCP 服务器正确实现了工具注册
2. 检查服务器是否正确响应 tools/list 请求
3. 验证服务器初始化流程
```

#### 问题3: 工具调用失败
```
解决方案:
1. 检查工具参数格式是否正确
2. 验证工具实现逻辑
3. 查看服务器日志输出
```

## 🔍 调试技巧

### 1. 查看服务器日志
在 VSCode 输出面板中选择对应的 MCP 服务器查看日志：
```
2025-07-25 23:37:27.251 [info] 正在启动服务器 hello-world
2025-07-25 23:37:27.254 [info] 连接类型: 正在启动
2025-07-25 23:37:27.256 [info] 服务器启动: 正在启动
2025-07-25 23:37:27.317 [info] Discovered 3 tools
```

### 2. 手动测试 MCP 服务器
可以创建测试脚本验证服务器功能：
```python
# test_mcp.py - 手动测试 MCP 服务器
import json
import subprocess

# 发送初始化消息
init_message = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {"protocolVersion": "2024-11-05"}
}
```

### 3. 验证工具注册
确保 MCP 服务器正确注册工具：
```python
@mcp.tool()
def your_tool(param: str) -> str:
    """工具描述"""
    return f"结果: {param}"
```

## 🌟 最佳实践

### 1. 配置管理
- 使用版本控制管理 `.vscode/mcp.json`
- 为不同环境创建不同的配置
- 添加配置文档说明

### 2. 错误处理
- 在 MCP 服务器中添加完善的错误处理
- 提供有意义的错误消息
- 记录详细的调试日志

### 3. 性能优化
- 避免在工具中执行耗时操作
- 使用异步处理提高响应速度
- 合理设计工具的输入输出格式

## 🔗 相关资源

- [MCP 官方文档](https://modelcontextprotocol.io)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [VSCode MCP 扩展文档](https://code.visualstudio.com/docs/copilot/chat/mcp-servers)
- [MCP 服务器示例](https://github.com/modelcontextprotocol/servers)

## 📝 更新记录

- **2025-07-25**: 初始版本，基于 Hello World MCP 服务器测试经验
- **待更新**: 添加更多配置示例和故障排除指南

---

*本文档基于实际测试经验编写，将随着项目进展持续更新。*
