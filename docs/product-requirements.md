# 📋 Cosyee AI Chat MCP 服务器 - 产品需求文档

## 🎯 产品概述

### 核心需求
构建一个 MCP 服务器，用于管理和恢复 AI 对话上下文，解决长期对话中的上下文丢失问题。

### 目标用户
- 使用 VSCode + Copilot Chat 进行开发的程序员
- 需要长期、连续 AI 对话支持的用户
- 希望管理和回顾历史 AI 对话的用户

### 产品价值
- **提升效率**：避免重复解释背景，快速恢复对话上下文
- **知识积累**：将 AI 对话转化为可搜索的知识库
- **持续性**：支持跨会话的长期项目讨论

## 🔍 核心问题分析

### 问题1：上下文丢失
- **现状**：VSCode Copilot Chat 会话重启后丢失历史上下文
- **影响**：需要重复解释背景，降低效率
- **期望**：能够恢复和继续之前的对话

### 问题2：对话记录管理
- **现状**：重要的 AI 对话内容无法持久化保存
- **影响**：有价值的讨论和解决方案丢失
- **期望**：能够保存、搜索、回顾历史对话

### 问题3：知识积累
- **现状**：每次对话都是独立的，无法形成知识积累
- **影响**：重复询问相同问题，无法建立知识体系
- **期望**：基于历史对话提供更智能的回答

### 问题4：项目连续性
- **现状**：长期项目的讨论无法跨会话延续
- **影响**：项目上下文频繁丢失，影响开发效率
- **期望**：支持项目级别的对话管理和上下文恢复

## 🎯 功能需求

### 🧠 强化记忆机制（核心创新）
基于三工具循环的上下文保持设计，解决 AI 对话中的记忆丢失问题：

#### 1. **conversation_start 工具** - 记忆强化
- **功能**: 每次对话开始时"洗脑"AI，强化行为指令
- **强化内容**:
  - 明确告诉 AI 当前应该如何行为
  - 提供上下文感知能力指导
  - 强制执行完整的对话流程
- **返回**: 强化记忆指令文本，让 AI 重新"编程"自己

#### 2. **conversation_query 工具** - 上下文恢复
- **功能**: 根据关键词搜索并恢复历史上下文
- **恢复内容**:
  - 相关历史对话摘要
  - 用户偏好和讨论风格
  - 之前的讨论深度和背景
- **返回**: 上下文强化信息，不只是数据，还有行为指导

#### 3. **conversation_end 工具** - 记忆固化
- **功能**: 保存对话并分析生成下次强化数据
- **固化内容**:
  - 完整对话记录保存
  - 提取关键信息和用户偏好
  - 生成下次 conversation_start 的强化内容
- **返回**: 确认记忆已固化，为未来对话准备

### 核心功能（MVP）
1. **对话保存**
   - 手动保存当前对话内容
   - 自动保存重要对话片段
   - 支持添加标签和备注

2. **上下文恢复**
   - 根据关键词搜索相关历史对话
   - 智能提取和合并相关上下文
   - 生成上下文摘要供 AI 参考

3. **对话搜索**
   - 全文搜索历史对话内容
   - 按时间、标签、项目筛选
   - 语义搜索相关对话

4. **基础管理**
   - 查看对话列表
   - 删除无用对话
   - 导出对话记录

### 扩展功能（V2）
1. **智能摘要**
   - 自动生成对话摘要
   - 提取关键信息和决策点
   - 生成行动项清单

2. **项目管理**
   - 按项目组织对话
   - 项目级别的上下文管理
   - 项目进度跟踪

3. **知识图谱**
   - 构建对话间的关联关系
   - 发现知识点之间的联系
   - 推荐相关历史讨论

4. **个性化优化**
   - 基于历史对话优化回答
   - 学习用户偏好和习惯
   - 提供个性化建议

## ❓ 待解决的关键问题

### 🔧 技术问题
1. **数据存储设计**
   - 如何设计数据库结构存储对话？
   - 如何处理大量对话数据的存储和查询性能？
   - 如何设计索引优化搜索速度？

2. **上下文提取算法**
   - 如何从历史对话中提取有效上下文？
   - 如何判断对话的相关性和重要性？
   - 如何合并多个对话片段形成连贯上下文？

3. **语义搜索实现**
   - 如何实现智能的对话搜索？
   - 是否需要集成向量数据库？
   - 如何平衡搜索准确性和性能？

4. **性能优化**
   - 如何保证查询和恢复的速度？
   - 如何处理大量历史数据的性能问题？
   - 如何优化 MCP 调用的响应时间？

### 🎨 产品问题
1. **用户体验设计**
   - 如何让用户无感知地使用这些功能？
   - 如何设计直观的对话管理界面？
   - 如何平衡自动化和用户控制？

2. **数据隐私保护**
   - 如何保护用户的对话隐私？
   - 如何处理敏感信息的存储？
   - 是否需要本地存储选项？

3. **配置管理**
   - 如何让用户方便地配置数据库连接？
   - 如何处理不同用户的个性化设置？
   - 如何提供简单的安装和配置流程？

### 🔗 集成问题
1. **MCP 协议设计**
   - 如何设计符合 MCP 标准的工具接口？
   - 如何定义工具的输入输出格式？
   - 如何处理复杂的数据传递？

2. **VSCode 集成**
   - 如何与 VSCode Copilot Chat 无缝集成？
   - 如何获取当前对话内容？
   - 如何在 Copilot Chat 中展示历史上下文？

3. **数据库连接**
   - 如何处理 MySQL 连接配置？
   - 如何处理连接池和并发访问？
   - 如何处理数据库迁移和版本管理？

4. **生态系统兼容**
   - 如何与其他 MCP 服务器协同工作？
   - 如何处理数据格式的标准化？
   - 如何支持插件化扩展？

## 📊 成功指标

### 用户体验指标
- 上下文恢复成功率 > 90%
- 搜索响应时间 < 2秒
- 用户满意度评分 > 4.5/5

### 技术指标
- 系统可用性 > 99%
- 数据库查询性能 < 100ms
- MCP 调用响应时间 < 500ms

### 业务指标
- 用户留存率 > 80%
- 日活跃用户增长率 > 10%
- 功能使用频率 > 5次/天

## 🚀 开发计划

### 阶段1：需求分析和设计（当前）
- [ ] 完善产品需求文档
- [ ] 解决关键技术问题
- [ ] 确定技术架构方案
- [ ] 设计数据库结构

### 阶段2：MVP 开发
- [ ] 实现基础 MCP 服务器框架
- [ ] 实现对话保存功能
- [ ] 实现基础搜索功能
- [ ] 实现上下文恢复功能

### 阶段3：功能完善
- [ ] 优化搜索算法
- [ ] 添加对话管理功能
- [ ] 实现智能摘要
- [ ] 性能优化

### 阶段4：扩展功能
- [ ] 项目管理功能
- [ ] 知识图谱构建
- [ ] 个性化优化
- [ ] 生态系统集成

## 🔄 强化记忆机制的技术实现

### 三工具循环流程
```
用户提问 → conversation_start() → [查询上下文] → AI回答 → conversation_end()
```

### 接口设计详情

#### 1. **conversation_start 接口**
**后端接口**: `POST /api/conversations/start`

**功能**: 创建会话并返回强化记忆指令

**响应示例**:
```json
{
  "session_id": "session_20250726_001",
  "memory_reinforcement": "🧠 强化记忆机制，防止遗忘 ++++++++++++\n\n💡 指令强化:\n1. 认真回答用户问题\n2. 回答完毕后必须调用 conversation_end 工具记录对话\n3. 如需上下文，立即调用 conversation_query 工具\n4. 保持专业和准确性\n\n🎯 当前会话: 数据库设计相关讨论\n📝 记住: 每次都要完整执行 开始→回答→结束 的流程"
}
```

#### 2. **conversation_query 接口**
**后端接口**: `GET /api/conversations/query`

**功能**: 搜索历史对话并返回上下文强化信息

**响应示例**:
```json
{
  "context_summary": "📚 上下文恢复:\n\n🔍 相关历史:\n- 你之前帮用户设计过 MySQL 表结构\n- 讨论过数据库范式和索引优化\n- 用户偏好实用性建议，不喜欢纯理论\n\n💡 继续保持:\n- 提供具体代码示例\n- 解释设计原理\n- 给出最佳实践建议\n\n🎯 当前应该: 基于之前的讨论深度，继续提供专业的数据库设计指导"
}
```

#### 3. **conversation_end 接口**
**后端接口**: `POST /api/conversations/end`

**功能**: 保存对话并分析生成记忆强化数据

**响应示例**:
```json
{
  "status": "saved",
  "memory_analysis": {
    "key_points": ["用户关注表结构设计", "需要实际代码示例", "偏好 MySQL"],
    "user_style": "喜欢详细解释，需要实用建议",
    "next_reinforcement": "下次讨论数据库时，记住用户已有基础知识，可以深入讨论高级特性"
  }
}
```

### 强化记忆的核心原理
1. **每次都是新开始**: AI 不依赖长期记忆，而是每次被"重新编程"
2. **指令式强化**: 通过明确的指令告诉 AI 应该如何行为
3. **上下文注入**: 不只是数据，还有行为模式和用户偏好
4. **循环强化**: 每次结束都为下次开始准备强化内容

### Summary 生成策略
**采用程序化生成而非 AI 生成**：

**优势**：
- **可控性强**：完全按照预设规则生成，结果可预期
- **一致性好**：每次生成的格式和质量稳定
- **性能高**：不需要调用 AI API，响应速度快
- **成本低**：不产生额外的 AI 调用费用
- **可调试**：逻辑清晰，容易排查和优化

**实现方式**：
- **规则引擎**：基于历史对话数据应用预设规则
- **模板系统**：针对不同主题使用专门的摘要模板
- **配置化**：支持模板和规则的动态调整

## 📘 VSCode MCP 集成方式

### 配置文件
VSCode 中的 MCP 配置文件位于项目根目录：`.vscode/mcp.json`

### 配置格式
```json
{
  "servers": {
    "cosyee-aichat": {
      "type": "stdio",
      "command": "/path/to/python",
      "args": ["/path/to/cosyee-aichat-mcp.py"]
    }
  }
}
```

### 使用流程
1. **配置 MCP 服务器** - 在 `.vscode/mcp.json` 中添加配置
2. **启动服务器** - VSCode 命令面板 → `MCP: Show Installed Servers`
3. **使用工具** - Copilot Chat Agent 模式下调用 MCP 工具

### 验证测试
基于 Hello World MCP 服务器的测试验证：
- ✅ 服务器正常启动和运行
- ✅ 工具发现和注册（3个测试工具）
- ✅ 工具调用和响应正常
- ✅ VSCode 集成无缝工作

### 集成要点
- **路径配置**：使用绝对路径，确保 Python 环境正确
- **协议支持**：支持 stdio、sse、http 等传输方式
- **错误处理**：完善的日志和错误信息
- **性能要求**：工具响应时间 < 500ms

## ⚠️ 开发规范

### 命名确认原则
在系统构建过程中，所有重要的命名都需要用户确认后才能继续开发，包括但不限于：

#### 🏷️ 必须确认的命名
- **项目名称** - 如 `cosyee-aichat-mcp`
- **工具名称** - 如 `conversation_start`, `conversation_end`, `conversation_query`
- **数据库名称** - 如 `cosyee_conversations`
- **表名称** - 如 `conversations`, `conversation_tags`
- **字段名称** - 如 `user_message`, `ai_response`, `created_at`
- **配置文件名** - 如 `mcp-config.json`
- **API 端点** - 如 `/api/conversations/save`

#### 🚫 禁止自动生成命名
- 不得在未经确认的情况下创建文件、目录、数据库表
- 不得使用临时或占位符命名进行开发
- 所有命名必须经过讨论和明确确认

#### ✅ 命名确认流程
1. **提出命名建议** - 基于功能和上下文提出合理命名
2. **等待用户确认** - 明确询问用户是否同意该命名
3. **记录确认结果** - 在文档中记录已确认的命名
4. **开始实现** - 仅在确认后开始使用该命名进行开发

### 示例对话
```
AI: "我建议将三个核心工具命名为：
- conversation_start (开始工具)
- conversation_end (结束工具)
- conversation_query (查询工具)
您同意这些命名吗？"

用户: "同意" 或 "修改为 xxx"

AI: "✅ 已确认工具命名，开始实现..."
```

## 🤔 下一步行动

**我们需要按优先级逐个解决上述问题，确保每个问题都有明确的解决方案后再进入开发阶段。**

**建议从以下问题开始讨论：**
1. 数据存储设计 - 确定数据库结构
2. MCP 工具接口设计 - 确定对外 API
3. 用户体验流程设计 - 确定使用方式
4. 技术架构选择 - 确定实现方案

---

*本文档将随着问题的解决和需求的明确持续更新。*
