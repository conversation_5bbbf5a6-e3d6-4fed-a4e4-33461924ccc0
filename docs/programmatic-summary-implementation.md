# 🔧 程序化 Summary 生成实现文档

## 🎯 设计原则

### 为什么选择程序化生成？

| 特性 | 程序化生成 | AI 生成 |
|------|------------|---------|
| **可控性** | ✅ 完全可控，结果可预期 | ❌ 不可控，可能偏离预期 |
| **一致性** | ✅ 格式和质量稳定 | ❌ 每次结果可能不同 |
| **性能** | ✅ 响应快，无 API 延迟 | ❌ 需要额外 AI 调用 |
| **成本** | ✅ 无额外费用 | ❌ 消耗 AI tokens |
| **可调试** | ✅ 逻辑清晰，易排查 | ❌ 黑盒操作，难调试 |

**结论**：在上下文摘要生成场景中，**可控性和一致性比智能性更重要**。

## 🏗️ 架构设计

### 核心组件

```
ContextSummaryGenerator
├── TemplateManager      # 模板管理器
├── RuleEngine          # 规则引擎  
├── PreferenceAnalyzer  # 用户偏好分析器
├── HistoryExtractor    # 历史提取器
└── BehaviorGuideGen    # 行为指导生成器
```

### 数据流

```
历史对话数据 → 规则引擎分析 → 模板选择 → 内容填充 → 生成摘要
```

## 🔧 详细实现

### 1. 模板管理器

```python
class TemplateManager:
    def __init__(self, template_dir="templates"):
        self.template_dir = template_dir
        self.templates = {}
        self._load_templates()
    
    def _load_templates(self):
        """加载所有模板文件"""
        template_files = {
            'database': 'database_summary.txt',
            'programming': 'programming_summary.txt', 
            'general': 'general_summary.txt'
        }
        
        for category, filename in template_files.items():
            template_path = os.path.join(self.template_dir, filename)
            with open(template_path, 'r', encoding='utf-8') as f:
                self.templates[category] = f.read()
    
    def get_template(self, category):
        """获取指定类别的模板"""
        return self.templates.get(category, self.templates['general'])
    
    def reload_templates(self):
        """热重载模板"""
        self._load_templates()
```

### 2. 用户偏好分析器

```python
class PreferenceAnalyzer:
    def analyze(self, conversations):
        """分析用户偏好"""
        preferences = {
            'answer_style': self._analyze_answer_style(conversations),
            'technical_level': self._analyze_technical_level(conversations),
            'example_preference': self._analyze_example_preference(conversations),
            'theory_vs_practice': self._analyze_theory_practice(conversations)
        }
        return preferences
    
    def _analyze_answer_style(self, conversations):
        """分析回答风格偏好"""
        if not conversations:
            return 'balanced'
        
        avg_length = sum(len(c['ai_response']) for c in conversations) / len(conversations)
        
        if avg_length > 1000:
            return 'detailed'
        elif avg_length < 300:
            return 'concise'
        else:
            return 'balanced'
    
    def _analyze_technical_level(self, conversations):
        """分析技术水平"""
        technical_terms = [
            'algorithm', 'optimization', 'architecture', 'design pattern',
            'performance', 'scalability', 'concurrency', 'distributed'
        ]
        
        total_terms = 0
        for conv in conversations:
            question = conv['user_question'].lower()
            response = conv['ai_response'].lower()
            total_terms += sum(1 for term in technical_terms 
                             if term in question or term in response)
        
        avg_terms = total_terms / len(conversations) if conversations else 0
        
        if avg_terms > 3:
            return 'advanced'
        elif avg_terms > 1:
            return 'intermediate'
        else:
            return 'beginner'
    
    def _analyze_example_preference(self, conversations):
        """分析示例偏好"""
        example_keywords = ['example', 'code', 'demo', '示例', '例子', '代码']
        
        example_requests = sum(1 for conv in conversations
                             if any(keyword in conv['user_question'].lower()
                                   for keyword in example_keywords))
        
        return example_requests > len(conversations) * 0.3
    
    def _analyze_theory_practice(self, conversations):
        """分析理论vs实践偏好"""
        practice_keywords = ['practical', 'real', 'actual', '实际', '实用', '具体']
        theory_dislikes = ['too abstract', 'too theoretical', '太抽象', '太理论']
        
        practice_score = 0
        for conv in conversations:
            question = conv['user_question'].lower()
            feedback = conv.get('user_feedback', '').lower()
            
            if any(keyword in question for keyword in practice_keywords):
                practice_score += 1
            if any(keyword in feedback for keyword in theory_dislikes):
                practice_score += 2
        
        return 'practice_focused' if practice_score > 0 else 'balanced'
```

### 3. 历史提取器

```python
class HistoryExtractor:
    def extract_relevant_history(self, conversations, max_items=3):
        """提取相关历史"""
        if not conversations:
            return "- 这是你们的第一次对话"
        
        history_items = []
        
        for conv in conversations[:max_items]:
            item = self._format_history_item(conv)
            history_items.append(item)
        
        return '\n'.join(history_items)
    
    def _format_history_item(self, conv):
        """格式化单个历史项"""
        item = f"- 你之前帮用户讨论过 {conv['topic']}"
        
        # 添加用户反馈信息
        if conv.get('user_feedback'):
            feedback = conv['user_feedback']
            if any(word in feedback for word in ['需要', '希望', 'need', 'want']):
                item += f"，用户希望：{feedback}"
            elif any(word in feedback for word in ['不喜欢', '太', 'too', 'dislike']):
                item += f"，用户反馈：{feedback}"
        
        # 添加讨论深度标识
        response_length = len(conv['ai_response'])
        if response_length > 1500:
            item += "（深入详细讨论）"
        elif response_length > 800:
            item += "（中等深度讨论）"
        
        # 添加重要性标识
        if conv.get('importance') == 'high':
            item += " ⭐"
        
        return item
```

### 4. 行为指导生成器

```python
class BehaviorGuideGenerator:
    def __init__(self):
        self.guidance_templates = {
            'database': {
                'advanced': "基于用户的高级数据库知识，深入讨论 {keywords} 的优化策略和最佳实践",
                'intermediate': "基于用户的数据库基础，重点讲解 {keywords} 的实际应用和常见问题",
                'beginner': "从基础概念开始，循序渐进地介绍 {keywords} 的原理和使用方法"
            },
            'programming': {
                'advanced': "基于用户的编程经验，深入分析 {keywords} 的设计模式和架构考虑",
                'intermediate': "基于用户的编程基础，重点讲解 {keywords} 的实现方法和最佳实践",
                'beginner': "从基本语法开始，逐步介绍 {keywords} 的概念和简单应用"
            }
        }
    
    def generate_guidance(self, keywords, topic_category, user_preferences):
        """生成行为指导"""
        technical_level = user_preferences.get('technical_level', 'intermediate')
        
        template = self.guidance_templates.get(topic_category, {}).get(
            technical_level, 
            f"基于用户的讨论历史，继续提供关于 {keywords} 的专业指导"
        )
        
        guidance = template.format(keywords=keywords)
        
        # 根据用户偏好添加额外指导
        if user_preferences.get('example_preference'):
            guidance += "，包含具体的代码示例和实际案例"
        
        if user_preferences.get('theory_vs_practice') == 'practice_focused':
            guidance += "，重点关注实际应用，避免过多理论讲解"
        
        return guidance
```

### 5. 主生成器

```python
class ContextSummaryGenerator:
    def __init__(self, template_dir="templates"):
        self.template_manager = TemplateManager(template_dir)
        self.preference_analyzer = PreferenceAnalyzer()
        self.history_extractor = HistoryExtractor()
        self.behavior_guide_gen = BehaviorGuideGenerator()
    
    def generate_summary(self, conversations, keywords, topic_category='general'):
        """生成上下文摘要"""
        
        # 1. 分析用户偏好
        user_preferences = self.preference_analyzer.analyze(conversations)
        
        # 2. 提取相关历史
        relevant_history = self.history_extractor.extract_relevant_history(conversations)
        
        # 3. 生成用户偏好文本
        preference_text = self._format_user_preferences(user_preferences)
        
        # 4. 生成行为指导
        behavior_guidance = self.behavior_guide_gen.generate_guidance(
            keywords, topic_category, user_preferences
        )
        
        # 5. 获取模板并填充
        template = self.template_manager.get_template(topic_category)
        
        summary = template.format(
            relevant_history=relevant_history,
            user_preferences=preference_text,
            behavior_guidance=behavior_guidance,
            keywords=keywords
        )
        
        return summary
    
    def _format_user_preferences(self, preferences):
        """格式化用户偏好为文本"""
        pref_lines = []
        
        if preferences['answer_style'] == 'detailed':
            pref_lines.append("- 提供详细解释和完整示例")
        elif preferences['answer_style'] == 'concise':
            pref_lines.append("- 保持回答简洁明了")
        
        if preferences['example_preference']:
            pref_lines.append("- 包含具体的代码示例")
        
        if preferences['theory_vs_practice'] == 'practice_focused':
            pref_lines.append("- 重点关注实际应用，避免纯理论")
        
        return '\n'.join(pref_lines) if pref_lines else "- 保持专业和准确的回答风格"
```

## 📁 模板文件示例

### templates/database_summary.txt
```
📚 上下文恢复:

🔍 相关历史:
{relevant_history}

💡 继续保持:
{user_preferences}
- 提供可以直接使用的 SQL 语句
- 解释每个设计决策的实际意义
- 关注性能和可维护性

🎯 当前应该:
{behavior_guidance}

⚠️ 重要提醒:
- 回答完毕后必须调用 conversation_end 工具记录对话
- 如需查询更多相关历史，随时调用 conversation_query 工具
```

## 🚀 使用示例

```python
# 初始化生成器
generator = ContextSummaryGenerator("templates")

# 模拟历史对话数据
conversations = [
    {
        "topic": "MySQL索引优化",
        "user_question": "如何优化查询性能？",
        "ai_response": "可以通过创建合适的索引来优化查询性能...",
        "user_feedback": "需要更多实际例子",
        "importance": "high"
    }
]

# 生成摘要
summary = generator.generate_summary(
    conversations=conversations,
    keywords="数据库表结构设计", 
    topic_category="database"
)

print(summary)
```

这样的程序化实现确保了摘要生成的**可控性、一致性和高性能**！
