<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库路径 -->
    <localRepository>${user.home}/.m2/repository</localRepository>

    <!-- 纯国内镜像配置 (如果 Nexus 不可用时使用) -->
    <mirrors>
        <!-- 阿里云 Maven 镜像 (主要) -->
        <mirror>
            <id>aliyun-central</id>
            <mirrorOf>central</mirrorOf>
            <name><PERSON>yun Maven Central Mirror</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </mirror>
        
        <!-- 阿里云 Spring 镜像 -->
        <mirror>
            <id>aliyun-spring</id>
            <mirrorOf>spring-releases,spring-milestones</mirrorOf>
            <name><PERSON>yun Spring Repository</name>
            <url>https://maven.aliyun.com/repository/spring</url>
        </mirror>
        
        <!-- 阿里云 Google 镜像 -->
        <mirror>
            <id>aliyun-google</id>
            <mirrorOf>google</mirrorOf>
            <name>Aliyun Google Repository</name>
            <url>https://maven.aliyun.com/repository/google</url>
        </mirror>
        
        <!-- 华为云镜像 (备用) -->
        <mirror>
            <id>huawei-central</id>
            <mirrorOf>*</mirrorOf>
            <name>Huawei Maven Repository</name>
            <url>https://repo.huaweicloud.com/repository/maven/</url>
        </mirror>
    </mirrors>

    <!-- 配置文件 -->
    <profiles>
        <profile>
            <id>china-mirror</id>
            <repositories>
                <!-- 阿里云公共仓库 -->
                <repository>
                    <id>aliyun-public</id>
                    <name>Aliyun Public Repository</name>
                    <url>https://maven.aliyun.com/repository/public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                
                <!-- 阿里云 Spring 仓库 -->
                <repository>
                    <id>aliyun-spring</id>
                    <name>Aliyun Spring Repository</name>
                    <url>https://maven.aliyun.com/repository/spring</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            
            <pluginRepositories>
                <pluginRepository>
                    <id>aliyun-plugin</id>
                    <name>Aliyun Plugin Repository</name>
                    <url>https://maven.aliyun.com/repository/central</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <!-- 激活配置文件 -->
    <activeProfiles>
        <activeProfile>china-mirror</activeProfile>
    </activeProfiles>

</settings>
