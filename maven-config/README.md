# Maven + Nexus 配置说明

## 📋 配置概览

### Nexus 服务器信息
- **地址**: `http://************:8081`
- **版本**: 3.62.0 (通过 1Panel 安装)
- **管理界面**: `http://************:8081/#browse/browse`

### 可用仓库
- `maven-public` (group) - 公共组仓库，包含所有代理仓库
- `maven-central` (proxy) - Maven Central 代理仓库
- `maven-releases` (hosted) - Release 版本仓库
- `maven-snapshots` (hosted) - Snapshot 版本仓库

### 国内镜像配置
为了加速依赖下载，配置了以下国内镜像：
- **阿里云镜像**: `https://maven.aliyun.com/repository/central`
- **华为云镜像**: `https://repo.huaweicloud.com/repository/maven/`

### 仓库优先级
1. **Nexus 本地仓库** (优先) - 企业内部依赖和缓存
2. **阿里云镜像** (备用) - 国内高速访问
3. **华为云镜像** (备用) - 国内备选方案
4. **Maven Central** (最后) - 官方仓库

## 🛠️ 配置步骤

### 1. 复制 Maven 配置文件
```bash
# 复制到用户 Maven 配置目录
cp maven-config/settings.xml ~/.m2/settings.xml

# 或者设置 MAVEN_SETTINGS 环境变量
export MAVEN_SETTINGS=/path/to/cosyee-cloud/maven-config/settings.xml
```

### 2. 更新认证信息
编辑 `settings.xml` 文件，替换密码：
```xml
<server>
    <id>nexus-releases</id>
    <username>admin</username>
    <password>你的实际密码</password>
</server>
```

### 3. 验证配置
```bash
# 测试连接
mvn help:effective-settings

# 清理并重新下载依赖
mvn clean dependency:resolve
```

## 🔧 项目配置

### pom.xml 配置
每个项目的 `pom.xml` 已经配置了：
- **仓库配置** - 指向 Nexus 仓库
- **插件仓库配置** - 插件从 Nexus 下载
- **分发管理** - 发布到 Nexus 仓库

### 发布到 Nexus
```bash
# 发布 Release 版本
mvn clean deploy

# 发布 Snapshot 版本 (版本号包含 -SNAPSHOT)
mvn clean deploy
```

## 🚨 注意事项

### 1. 网络访问
确保开发环境能访问 `************:8081`

### 2. 认证信息
- 默认用户名: `admin`
- 密码需要从 1Panel 或 Nexus 管理界面获取

### 3. 仓库类型
- **maven-public**: 用于依赖下载（包含 Central 代理）
- **maven-releases**: 用于发布正式版本
- **maven-snapshots**: 用于发布开发版本

## 🔍 故障排除

### 1. 连接失败
```bash
# 检查网络连通性
curl -I http://************:8081/repository/maven-public/

# 检查认证
curl -u admin:password http://************:8081/repository/maven-public/
```

### 2. 依赖下载失败
```bash
# 清理本地仓库
rm -rf ~/.m2/repository

# 强制更新
mvn clean compile -U
```

### 3. 发布失败
- 检查 `distributionManagement` 配置
- 确认认证信息正确
- 验证仓库权限

## 📚 相关链接

- [Nexus Repository Manager 文档](https://help.sonatype.com/repomanager3)
- [Maven Settings 参考](https://maven.apache.org/settings.html)
- [1Panel 官方文档](https://1panel.cn/docs/)
