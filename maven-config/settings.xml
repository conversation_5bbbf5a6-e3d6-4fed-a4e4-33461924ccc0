<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库路径 -->
    <!-- <localRepository>${user.home}/.m2/repository</localRepository> -->

    <!-- 服务器认证信息 -->
    <servers>
        <!-- Nexus 服务器认证 -->
        <server>
            <id>nexus-releases</id>
            <username>admin</username>
            <password>Zz964604115</password>
        </server>
        <server>
            <id>nexus-snapshots</id>
            <username>admin</username>
            <password>Zz964604115</password>
        </server>
        <server>
            <id>nexus-public</id>
            <username>admin</username>
            <password>Zz964604115</password>
        </server>
        <server>
            <id>maven-central</id>
            <username>admin</username>
            <password>Zz964604115</password>
        </server>
    </servers>

    <!-- 镜像配置 -->
    <mirrors>
        <!-- 使用 Nexus 作为主要镜像，包含国内镜像代理 -->
        <mirror>
            <id>nexus-public</id>
            <mirrorOf>central</mirrorOf>
            <name>Nexus Public Repository Group</name>
            <url>http://192.168.3.15:8081/repository/maven-public/</url>
        </mirror>
    </mirrors>

    <!-- 配置文件 -->
    <profiles>
        <!-- Nexus 仓库配置 -->
        <profile>
            <id>nexus</id>
            <repositories>
                <!-- Maven Public 组仓库 (包含 central 代理和国内镜像) -->
                <repository>
                    <id>nexus-public</id>
                    <name>Nexus Public Repository Group</name>
                    <url>http://192.168.3.15:8081/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>daily</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>

                <!-- Release 仓库 -->
                <repository>
                    <id>nexus-releases</id>
                    <name>Nexus Releases Repository</name>
                    <url>http://192.168.3.15:8081/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>

                <!-- Snapshot 仓库 -->
                <repository>
                    <id>nexus-snapshots</id>
                    <name>Nexus Snapshots Repository</name>
                    <url>http://192.168.3.15:8081/repository/maven-snapshots/</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>
            </repositories>

            <!-- 插件仓库 -->
            <pluginRepositories>
                <pluginRepository>
                    <id>nexus-public</id>
                    <name>Nexus Public Repository Group</name>
                    <url>http://192.168.3.15:8081/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <!-- 激活配置文件 -->
    <activeProfiles>
        <activeProfile>nexus</activeProfile>
    </activeProfiles>

</settings>
