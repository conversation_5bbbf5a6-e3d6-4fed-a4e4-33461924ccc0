2025-07-28 23:09:57 [main] INFO  c.c.c.ConversationApplication - Starting ConversationApplication using Java 1.8.0_422 on danedeMacBook-Pro.local with PID 1420 (/Users/<USER>/cosyee-cloud/cosyee-aichat-conversation/target/classes started by dane in /Users/<USER>/cosyee-cloud/cosyee-aichat-conversation)
2025-07-28 23:09:57 [main] DEBUG c.c.c.ConversationApplication - Running with Spring Boot v2.6.15, Spring v5.3.27
2025-07-28 23:09:57 [main] INFO  c.c.c.ConversationApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-28 23:09:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 23:09:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 23:09:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-07-28 23:09:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9001 (http)
2025-07-28 23:09:58 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-28 23:09:58 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-07-28 23:09:58 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-28 23:09:58 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 589 ms
2025-07-28 23:09:58 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.cosyee.conversation.mapper.ConversationMapper.searchByKeyword] is ignored, because it exists, maybe from xml file
2025-07-28 23:09:58 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.cosyee.conversation.mapper.ConversationMapper.findByUserIdOrderByCreatedAtDesc] is ignored, because it exists, maybe from xml file
2025-07-28 23:09:58 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-28 23:09:58 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 9 mappings in 'requestMappingHandlerMapping'
2025-07-28 23:09:58 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-28 23:09:58 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-28 23:09:58 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-28 23:09:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9001 (http) with context path ''
2025-07-28 23:09:58 [main] INFO  c.c.c.ConversationApplication - Started ConversationApplication in 1.341 seconds (JVM running for 1.492)
2025-07-28 23:10:12 [http-nio-9001-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 23:10:12 [http-nio-9001-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@4df85ca3
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@4adf5865
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-28 23:10:12 [http-nio-9001-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/conversations/health", parameters={}
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.cosyee.conversation.controller.ConversationController#healthCheck()
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [{endpoints=[Ljava.lang.String;@298eaa1c, service=Conversation Service, status=UP, timestamp=17537154 (truncated)...]
2025-07-28 23:10:12 [http-nio-9001-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-28 23:10:27 [http-nio-9001-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/conversations/start", parameters={}
2025-07-28 23:10:27 [http-nio-9001-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.cosyee.conversation.controller.ConversationController#startConversation(Map)
2025-07-28 23:10:27 [http-nio-9001-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{topic=MySQL数据库设计, topic_category=database, user_id=test_user}]
2025-07-28 23:10:27 [http-nio-9001-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-28 23:10:27 [http-nio-9001-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [{user_id=test_user, session_id=session_1753715427081, topic=MySQL数据库设计, started_at=2025-07-28T23:10: (truncated)...]
2025-07-28 23:10:27 [http-nio-9001-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-28 23:10:38 [http-nio-9001-exec-3] INFO  o.a.coyote.http11.Http11Processor - Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in the request target [/api/conversations/query?keywords=0xe60x950xb00xe60x8d0xae0xe50xba0x93&user_id=test_user&limit=5 ]. The valid characters are defined in RFC 7230 and RFC 3986
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:482)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:263)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/conversations/end", parameters={}
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.cosyee.conversation.controller.ConversationController#endConversation(ConversationRequest)
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [ConversationRequest{topic='MySQL数据库设计', importance=MEDIUM, userId='default_user', sessionId='null',  (truncated)...]
2025-07-28 23:10:52 [http-nio-9001-exec-4] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> com.cosyee.conversation.controller.ConversationController.endConversation(com.cosyee.conversation.dto.ConversationRequest) with 2 errors: [Field error in object 'conversationRequest' on field 'aiResponse': rejected value [null]; codes [NotBlank.conversationRequest.aiResponse,NotBlank.aiResponse,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [conversationRequest.aiResponse,aiResponse]; arguments []; default message [aiResponse]]; default message [AI回答不能为空]] [Field error in object 'conversationRequest' on field 'userQuestion': rejected value [null]; codes [NotBlank.conversationRequest.userQuestion,NotBlank.userQuestion,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [conversationRequest.userQuestion,userQuestion]; arguments []; default message [userQuestion]]; default message [用户问题不能为空]] ]
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 400 BAD_REQUEST
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - "ERROR" dispatch for POST "/error", parameters={}
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [{timestamp=Mon Jul 28 23:10:52 CST 2025, status=400, error=Bad Request, path=/api/conversations/end}]
2025-07-28 23:10:52 [http-nio-9001-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Exiting from "ERROR" dispatch, status 400
2025-07-28 23:11:11 [http-nio-9001-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/conversations/query?keywords=database&user_id=test_user&limit=5", parameters={masked}
2025-07-28 23:11:11 [http-nio-9001-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.cosyee.conversation.controller.ConversationController#queryConversations(String, String, String, int)
2025-07-28 23:11:11 [http-nio-9001-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-28 23:11:11 [http-nio-9001-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-28 23:11:11 [http-nio-9001-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-28 23:11:11 [http-nio-9001-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [{total=0, keywords=database, user_id=test_user, limit=5, topic=, conversations=[]}]
2025-07-28 23:11:11 [http-nio-9001-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-28 23:11:21 [http-nio-9001-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/conversations/end", parameters={}
2025-07-28 23:11:21 [http-nio-9001-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.cosyee.conversation.controller.ConversationController#endConversation(ConversationRequest)
2025-07-28 23:11:21 [http-nio-9001-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [ConversationRequest{topic='MySQL数据库设计', importance=MEDIUM, userId='test_user', sessionId='null', tag (truncated)...]
2025-07-28 23:11:21 [http-nio-9001-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-28 23:11:21 [http-nio-9001-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [{conversation_id=1, memory_analysis={next_reinforcement=下次讨论MySQL数据库设计时，继续保持偏好实用性指导，提供具体可操作的建议, user (truncated)...]
2025-07-28 23:11:21 [http-nio-9001-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-28 23:11:28 [http-nio-9001-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/conversations/query?keywords=MySQL&user_id=test_user&limit=5", parameters={masked}
2025-07-28 23:11:28 [http-nio-9001-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.cosyee.conversation.controller.ConversationController#queryConversations(String, String, String, int)
2025-07-28 23:11:28 [http-nio-9001-exec-9] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-28 23:11:28 [http-nio-9001-exec-9] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [{total=1, keywords=MySQL, user_id=test_user, limit=5, topic=, conversations=[Conversation{id=1, user (truncated)...]
2025-07-28 23:11:28 [http-nio-9001-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
