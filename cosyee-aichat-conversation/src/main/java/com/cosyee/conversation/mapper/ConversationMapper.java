package com.cosyee.conversation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosyee.conversation.entity.Conversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 对话记录 Mapper 接口
 */
@Mapper
public interface ConversationMapper extends BaseMapper<Conversation> {

    /**
     * 根据用户ID查找对话记录
     */
    @Select("SELECT * FROM cosyee_conversations WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<Conversation> findByUserIdOrderByCreatedAtDesc(@Param("userId") String userId);

    /**
     * 根据用户ID分页查找对话记录
     */
    @Select("SELECT * FROM cosyee_conversations WHERE user_id = #{userId} ORDER BY created_at DESC")
    Page<Conversation> findByUserIdOrderByCreatedAtDesc(@Param("userId") String userId, Page<Conversation> page);

    /**
     * 根据会话ID查找对话记录
     */
    @Select("SELECT * FROM cosyee_conversations WHERE session_id = #{sessionId} ORDER BY created_at DESC")
    List<Conversation> findBySessionIdOrderByCreatedAtDesc(@Param("sessionId") String sessionId);

    /**
     * 根据主题查找对话记录
     */
    @Select("SELECT * FROM cosyee_conversations WHERE topic LIKE CONCAT('%', #{topic}, '%') ORDER BY created_at DESC")
    List<Conversation> findByTopicContainingIgnoreCaseOrderByCreatedAtDesc(@Param("topic") String topic);

    /**
     * 根据重要性级别查找对话记录
     */
    @Select("SELECT * FROM cosyee_conversations WHERE importance = #{importance} ORDER BY created_at DESC")
    List<Conversation> findByImportanceOrderByCreatedAtDesc(@Param("importance") String importance);

    /**
     * 根据用户ID和主题查找对话记录
     */
    @Select("SELECT * FROM cosyee_conversations WHERE user_id = #{userId} AND topic LIKE CONCAT('%', #{topic}, '%') ORDER BY created_at DESC")
    List<Conversation> findByUserIdAndTopicContainingIgnoreCaseOrderByCreatedAtDesc(
            @Param("userId") String userId, @Param("topic") String topic);

    /**
     * 根据时间范围查找对话记录
     */
    @Select("SELECT * FROM cosyee_conversations WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<Conversation> findByCreatedAtBetweenOrderByCreatedAtDesc(
            @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 全文搜索对话内容
     */
    @Select("SELECT * FROM cosyee_conversations WHERE user_id = #{userId} AND (" +
            "topic LIKE CONCAT('%', #{keyword}, '%') OR " +
            "user_question LIKE CONCAT('%', #{keyword}, '%') OR " +
            "ai_response LIKE CONCAT('%', #{keyword}, '%') OR " +
            "tags LIKE CONCAT('%', #{keyword}, '%')" +
            ") ORDER BY created_at DESC")
    List<Conversation> searchByKeyword(@Param("userId") String userId, @Param("keyword") String keyword);

    /**
     * 分页全文搜索对话内容
     */
    @Select("SELECT * FROM cosyee_conversations WHERE user_id = #{userId} AND (" +
            "topic LIKE CONCAT('%', #{keyword}, '%') OR " +
            "user_question LIKE CONCAT('%', #{keyword}, '%') OR " +
            "ai_response LIKE CONCAT('%', #{keyword}, '%') OR " +
            "tags LIKE CONCAT('%', #{keyword}, '%')" +
            ") ORDER BY created_at DESC")
    Page<Conversation> searchByKeyword(@Param("userId") String userId, 
                                     @Param("keyword") String keyword, 
                                     Page<Conversation> page);

    /**
     * 根据主题和用户ID搜索，支持限制结果数量
     */
    @Select("SELECT * FROM cosyee_conversations WHERE user_id = #{userId} " +
            "AND (#{topic} IS NULL OR #{topic} = '' OR topic LIKE CONCAT('%', #{topic}, '%')) " +
            "AND (#{keyword} IS NULL OR #{keyword} = '' OR " +
            "user_question LIKE CONCAT('%', #{keyword}, '%') OR " +
            "ai_response LIKE CONCAT('%', #{keyword}, '%') OR " +
            "tags LIKE CONCAT('%', #{keyword}, '%')" +
            ") ORDER BY created_at DESC LIMIT #{limit}")
    List<Conversation> findConversationsWithFilters(@Param("userId") String userId,
                                                   @Param("topic") String topic,
                                                   @Param("keyword") String keyword,
                                                   @Param("limit") int limit);

    /**
     * 统计用户的对话总数
     */
    @Select("SELECT COUNT(*) FROM cosyee_conversations WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") String userId);

    /**
     * 统计用户在指定时间范围内的对话数量
     */
    @Select("SELECT COUNT(*) FROM cosyee_conversations WHERE user_id = #{userId} AND created_at BETWEEN #{startTime} AND #{endTime}")
    long countByUserIdAndCreatedAtBetween(@Param("userId") String userId, 
                                        @Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查找用户最近的对话记录
     */
    @Select("SELECT * FROM cosyee_conversations WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT 1")
    Conversation findFirstByUserIdOrderByCreatedAtDesc(@Param("userId") String userId);

    /**
     * 根据标签搜索对话（JSON 字段搜索）
     */
    @Select("SELECT * FROM cosyee_conversations WHERE user_id = #{userId} AND tags IS NOT NULL AND JSON_CONTAINS(tags, JSON_QUOTE(#{tag}))")
    List<Conversation> findByTag(@Param("userId") String userId, @Param("tag") String tag);

    /**
     * 删除指定时间之前的对话记录（数据清理）
     */
    @Select("DELETE FROM cosyee_conversations WHERE created_at < #{cutoffTime}")
    void deleteByCreatedAtBefore(@Param("cutoffTime") LocalDateTime cutoffTime);
}
