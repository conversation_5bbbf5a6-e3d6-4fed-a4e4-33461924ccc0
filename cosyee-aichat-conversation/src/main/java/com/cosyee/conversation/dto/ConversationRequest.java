package com.cosyee.conversation.dto;

import com.cosyee.conversation.entity.Conversation;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 对话请求 DTO
 */
public class ConversationRequest {

    @NotBlank(message = "用户问题不能为空")
    @Size(max = 10000, message = "用户问题长度不能超过10000字符")
    private String userQuestion;

    @NotBlank(message = "AI回答不能为空")
    @Size(max = 50000, message = "AI回答长度不能超过50000字符")
    private String aiResponse;

    @NotBlank(message = "对话主题不能为空")
    @Size(max = 200, message = "对话主题长度不能超过200字符")
    private String topic;

    @NotNull(message = "重要性级别不能为空")
    private Conversation.ImportanceLevel importance;

    private List<String> tags;

    @Size(max = 100, message = "用户ID长度不能超过100字符")
    private String userId = "default_user";

    private String sessionId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    // 构造函数
    public ConversationRequest() {}

    public ConversationRequest(String userQuestion, String aiResponse, String topic, 
                             Conversation.ImportanceLevel importance) {
        this.userQuestion = userQuestion;
        this.aiResponse = aiResponse;
        this.topic = topic;
        this.importance = importance;
    }

    // Getter 和 Setter 方法
    public String getUserQuestion() {
        return userQuestion;
    }

    public void setUserQuestion(String userQuestion) {
        this.userQuestion = userQuestion;
    }

    public String getAiResponse() {
        return aiResponse;
    }

    public void setAiResponse(String aiResponse) {
        this.aiResponse = aiResponse;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public Conversation.ImportanceLevel getImportance() {
        return importance;
    }

    public void setImportance(Conversation.ImportanceLevel importance) {
        this.importance = importance;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "ConversationRequest{" +
                "topic='" + topic + '\'' +
                ", importance=" + importance +
                ", userId='" + userId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", tagsCount=" + (tags != null ? tags.size() : 0) +
                '}';
    }
}
