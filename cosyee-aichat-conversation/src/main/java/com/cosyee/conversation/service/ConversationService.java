package com.cosyee.conversation.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cosyee.conversation.dto.ConversationRequest;
import com.cosyee.conversation.entity.Conversation;
import com.cosyee.conversation.mapper.ConversationMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 对话服务业务逻辑
 */
@Service
public class ConversationService {

    @Autowired
    private ConversationMapper conversationMapper;

    @Autowired
    private RedisService redisService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 开始对话会话
     */
    public Map<String, Object> startConversation(String sessionId, String topic, 
                                                String topicCategory, String userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 生成会话ID（如果没有提供）
            if (!StringUtils.hasText(sessionId)) {
                sessionId = "session_" + System.currentTimeMillis();
            }
            
            // 缓存会话信息到Redis
            Map<String, Object> sessionInfo = new HashMap<>();
            sessionInfo.put("session_id", sessionId);
            sessionInfo.put("topic", topic);
            sessionInfo.put("topic_category", topicCategory);
            sessionInfo.put("user_id", userId);
            sessionInfo.put("started_at", LocalDateTime.now().toString());
            
            String sessionKey = "conversation_session:" + sessionId;
            redisService.setString(sessionKey, objectMapper.writeValueAsString(sessionInfo));
            
            result.put("session_id", sessionId);
            result.put("topic", topic);
            result.put("topic_category", topicCategory);
            result.put("user_id", userId);
            result.put("status", "started");
            result.put("started_at", LocalDateTime.now());
            
            return result;
            
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to start conversation session", e);
        }
    }

    /**
     * 查询历史对话
     */
    public Map<String, Object> queryConversations(String keywords, String topic, 
                                                 String userId, int limit) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 使用Mapper查询对话记录
            List<Conversation> conversations = conversationMapper.findConversationsWithFilters(
                userId, topic, keywords, limit
            );
            
            result.put("conversations", conversations);
            result.put("total", conversations.size());
            result.put("keywords", keywords);
            result.put("topic", topic);
            result.put("user_id", userId);
            result.put("limit", limit);
            
            return result;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to query conversations", e);
        }
    }

    /**
     * 结束对话并保存记录
     */
    public Map<String, Object> endConversation(ConversationRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建对话记录
            Conversation conversation = new Conversation();
            conversation.setUserId(request.getUserId());
            conversation.setTopic(request.getTopic());
            conversation.setUserQuestion(request.getUserQuestion());
            conversation.setAiResponse(request.getAiResponse());
            conversation.setImportance(request.getImportance());
            conversation.setSessionId(request.getSessionId());
            
            // 处理标签（转换为JSON字符串）
            if (request.getTags() != null && !request.getTags().isEmpty()) {
                conversation.setTags(objectMapper.writeValueAsString(request.getTags()));
            }
            
            // 保存到数据库
            int insertResult = conversationMapper.insert(conversation);
            
            if (insertResult > 0) {
                result.put("conversation_id", conversation.getId());
                result.put("status", "saved");
                result.put("message", "对话记录保存成功");
                
                // 生成记忆分析（简化版）
                Map<String, Object> memoryAnalysis = generateMemoryAnalysis(conversation);
                result.put("memory_analysis", memoryAnalysis);
                
            } else {
                throw new RuntimeException("Failed to save conversation record");
            }
            
            return result;
            
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to process conversation tags", e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to end conversation", e);
        }
    }

    /**
     * 生成记忆分析（简化实现）
     */
    private Map<String, Object> generateMemoryAnalysis(Conversation conversation) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 提取关键点
        String[] keyPoints = extractKeyPoints(conversation.getUserQuestion(), conversation.getAiResponse());
        analysis.put("key_points", keyPoints);
        
        // 分析用户风格
        String userStyle = analyzeUserStyle(conversation.getUserQuestion());
        analysis.put("user_style", userStyle);
        
        // 生成下次强化建议
        String nextReinforcement = generateNextReinforcement(conversation.getTopic(), userStyle);
        analysis.put("next_reinforcement", nextReinforcement);
        
        return analysis;
    }

    /**
     * 提取关键点（简化实现）
     */
    private String[] extractKeyPoints(String question, String response) {
        // 简化实现：基于长度和关键词
        if (response.length() > 500) {
            return new String[]{"详细讨论了" + extractMainTopic(question), "提供了具体实现方案"};
        } else {
            return new String[]{"简要回答了" + extractMainTopic(question)};
        }
    }

    /**
     * 提取主要话题（简化实现）
     */
    private String extractMainTopic(String question) {
        if (question.contains("数据库")) return "数据库相关问题";
        if (question.contains("编程") || question.contains("代码")) return "编程相关问题";
        if (question.contains("设计")) return "设计相关问题";
        return "技术问题";
    }

    /**
     * 分析用户风格（简化实现）
     */
    private String analyzeUserStyle(String question) {
        if (question.length() > 100) {
            return "偏好详细描述问题";
        } else if (question.contains("如何") || question.contains("怎么")) {
            return "偏好实用性指导";
        } else {
            return "简洁直接的提问风格";
        }
    }

    /**
     * 生成下次强化建议（简化实现）
     */
    private String generateNextReinforcement(String topic, String userStyle) {
        return String.format("下次讨论%s时，继续保持%s，提供具体可操作的建议", topic, userStyle);
    }
}
