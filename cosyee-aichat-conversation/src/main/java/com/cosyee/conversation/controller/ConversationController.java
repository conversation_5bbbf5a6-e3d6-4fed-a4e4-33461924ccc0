package com.cosyee.conversation.controller;

import com.cosyee.conversation.dto.ConversationRequest;
import com.cosyee.conversation.entity.Conversation;
import com.cosyee.conversation.service.ConversationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 对话管理控制器
 */
@RestController
@RequestMapping("/api/conversations")
@Validated
public class ConversationController {

    @Autowired
    private ConversationService conversationService;

    /**
     * 开始对话会话
     * POST /api/conversations/start
     */
    @PostMapping("/start")
    public ResponseEntity<Map<String, Object>> startConversation(@RequestBody Map<String, Object> request) {
        try {
            String sessionId = (String) request.get("session_id");
            String topic = (String) request.get("topic");
            String topicCategory = (String) request.get("topic_category");
            String userId = (String) request.getOrDefault("user_id", "default_user");

            // 参数验证
            if (topic == null || topic.trim().isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "topic参数不能为空");
                errorResponse.put("status", "error");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            Map<String, Object> result = conversationService.startConversation(
                sessionId, topic, topicCategory, userId
            );

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "启动对话会话失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 查询历史对话
     * GET /api/conversations/query
     */
    @GetMapping("/query")
    public ResponseEntity<Map<String, Object>> queryConversations(
            @RequestParam @NotBlank(message = "keywords参数不能为空") String keywords,
            @RequestParam(required = false, defaultValue = "") String topic,
            @RequestParam(required = false, defaultValue = "default_user") String user_id,
            @RequestParam(required = false, defaultValue = "5") @Min(1) @Max(50) int limit) {
        
        try {
            Map<String, Object> result = conversationService.queryConversations(
                keywords, topic, user_id, limit
            );

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "查询对话失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 结束对话并保存记录
     * POST /api/conversations/end
     */
    @PostMapping("/end")
    public ResponseEntity<Map<String, Object>> endConversation(@Valid @RequestBody ConversationRequest request) {
        try {
            // 设置默认值
            if (request.getUserId() == null || request.getUserId().trim().isEmpty()) {
                request.setUserId("default_user");
            }
            
            if (request.getImportance() == null) {
                request.setImportance(Conversation.ImportanceLevel.MEDIUM);
            }

            Map<String, Object> result = conversationService.endConversation(request);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "保存对话记录失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取用户的对话历史（额外的便民接口）
     * GET /api/conversations/history/{userId}
     */
    @GetMapping("/history/{userId}")
    public ResponseEntity<Map<String, Object>> getUserConversationHistory(
            @PathVariable String userId,
            @RequestParam(required = false, defaultValue = "10") @Min(1) @Max(100) int limit) {
        
        try {
            Map<String, Object> result = conversationService.queryConversations(
                "", "", userId, limit
            );

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取用户对话历史失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 健康检查接口（对话服务专用）
     * GET /api/conversations/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "Conversation Service");
        response.put("status", "UP");
        response.put("timestamp", System.currentTimeMillis());
        response.put("endpoints", new String[]{
            "POST /api/conversations/start",
            "GET /api/conversations/query", 
            "POST /api/conversations/end",
            "GET /api/conversations/history/{userId}"
        });
        
        return ResponseEntity.ok(response);
    }
}
