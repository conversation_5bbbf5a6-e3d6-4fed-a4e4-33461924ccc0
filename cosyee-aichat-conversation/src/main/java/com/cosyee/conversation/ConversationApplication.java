package com.cosyee.conversation;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.cosyee.conversation.service.RedisService;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Cosyee AI Chat Conversation Service 应用启动类
 */
@SpringBootApplication
@MapperScan("com.cosyee.conversation.mapper")
@RestController
public class ConversationApplication {

    @Autowired
    private RedisService redisService;

    public static void main(String[] args) {
        SpringApplication.run(ConversationApplication.class, args);
    }

    /**
     * MyBatis-Plus 分页插件配置
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/api/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "Cosyee AI Chat Conversation Service");
        response.put("version", "1.0.0");
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now());
        response.put("description", "Spring Boot service for managing AI conversation records");

        // 测试 Redis 连接
        try {
            redisService.setString("health_check", "OK");
            String redisStatus = redisService.getString("health_check");
            response.put("redis_status", "OK".equals(redisStatus) ? "UP" : "DOWN");
            redisService.delete("health_check");
        } catch (Exception e) {
            response.put("redis_status", "DOWN");
            response.put("redis_error", e.getMessage());
        }

        return response;
    }

    /**
     * 服务信息端点
     */
    @GetMapping("/")
    public Map<String, Object> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "Cosyee AI Chat Conversation Service");
        response.put("version", "1.0.0");
        response.put("status", "running");
        response.put("timestamp", LocalDateTime.now());
        response.put("endpoints", new String[]{
            "POST /api/conversations/start - 开始对话会话",
            "POST /api/conversations/end - 结束对话并保存",
            "GET /api/conversations/query - 查询历史对话",
            "GET /api/health - 健康检查"
        });
        response.put("features", new String[]{
            "Conversation Management",
            "Context Search",
            "Tag Support",
            "MySQL Storage"
        });
        return response;
    }
}
