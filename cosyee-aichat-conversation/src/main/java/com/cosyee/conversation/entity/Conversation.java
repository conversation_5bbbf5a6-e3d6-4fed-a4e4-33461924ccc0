package com.cosyee.conversation.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 对话记录实体类
 */
@TableName("cosyee_conversations")
public class Conversation {

    @TableId(type = IdType.AUTO)
    private Long id;

    @NotBlank(message = "用户ID不能为空")
    @Size(max = 100, message = "用户ID长度不能超过100字符")
    @TableField("user_id")
    private String userId;

    @NotBlank(message = "对话主题不能为空")
    @Size(max = 200, message = "对话主题长度不能超过200字符")
    @TableField("topic")
    private String topic;

    @NotBlank(message = "用户问题不能为空")
    @TableField("user_question")
    private String userQuestion;

    @NotBlank(message = "AI回答不能为空")
    @TableField("ai_response")
    private String aiResponse;

    @NotNull(message = "重要性级别不能为空")
    @TableField("importance")
    private ImportanceLevel importance;

    @TableField("tags")
    private String tags; // JSON 格式存储标签列表

    @TableField("session_id")
    private String sessionId;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 构造函数
    public Conversation() {}

    public Conversation(String userId, String topic, String userQuestion, 
                       String aiResponse, ImportanceLevel importance) {
        this.userId = userId;
        this.topic = topic;
        this.userQuestion = userQuestion;
        this.aiResponse = aiResponse;
        this.importance = importance;
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getUserQuestion() {
        return userQuestion;
    }

    public void setUserQuestion(String userQuestion) {
        this.userQuestion = userQuestion;
    }

    public String getAiResponse() {
        return aiResponse;
    }

    public void setAiResponse(String aiResponse) {
        this.aiResponse = aiResponse;
    }

    public ImportanceLevel getImportance() {
        return importance;
    }

    public void setImportance(ImportanceLevel importance) {
        this.importance = importance;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "Conversation{" +
                "id=" + id +
                ", userId='" + userId + '\'' +
                ", topic='" + topic + '\'' +
                ", importance=" + importance +
                ", sessionId='" + sessionId + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }

    /**
     * 重要性级别枚举
     */
    public enum ImportanceLevel {
        LOW("低"),
        MEDIUM("中"),
        HIGH("高");

        private final String description;

        ImportanceLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
