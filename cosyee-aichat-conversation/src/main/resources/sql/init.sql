-- 创建数据库
CREATE DATABASE IF NOT EXISTS cosyee_aichat_conversation 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE cosyee_aichat_conversation;

-- 创建对话记录表
CREATE TABLE IF NOT EXISTS cosyee_conversations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
    topic VARCHAR(200) NOT NULL COMMENT '对话主题',
    user_question TEXT NOT NULL COMMENT '用户问题',
    ai_response TEXT NOT NULL COMMENT 'AI回答',
    importance ENUM('LOW', 'MEDIUM', 'HIGH') NOT NULL DEFAULT 'MEDIUM' COMMENT '重要性级别',
    tags JSON COMMENT '标签列表(JSON格式)',
    session_id VARCHAR(100) COMMENT '会话ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对话记录表';

-- 创建索引
CREATE INDEX idx_user_id ON cosyee_conversations(user_id);
CREATE INDEX idx_topic ON cosyee_conversations(topic);
CREATE INDEX idx_created_at ON cosyee_conversations(created_at);
CREATE INDEX idx_importance ON cosyee_conversations(importance);
CREATE INDEX idx_session_id ON cosyee_conversations(session_id);

-- 插入测试数据
INSERT INTO cosyee_conversations (user_id, topic, user_question, ai_response, importance, tags, session_id) VALUES
('test_user', 'Spring Boot 开发', '如何配置 MyBatis-Plus？', 
'配置 MyBatis-Plus 需要以下步骤：\n1. 添加依赖\n2. 配置数据源\n3. 创建实体类\n4. 创建 Mapper 接口', 
'HIGH', '["Spring Boot", "MyBatis-Plus", "配置"]', 'session_001'),

('test_user', 'MySQL 优化', '如何优化 MySQL 查询性能？', 
'MySQL 查询优化可以从以下几个方面入手：\n1. 合理使用索引\n2. 优化 SQL 语句\n3. 分析执行计划\n4. 调整数据库参数', 
'MEDIUM', '["MySQL", "性能优化", "索引"]', 'session_002'),

('test_user', 'Java 并发', 'Java 中如何处理并发问题？', 
'Java 并发处理主要包括：\n1. 使用 synchronized 关键字\n2. 使用 Lock 接口\n3. 使用线程池\n4. 使用并发集合类', 
'HIGH', '["Java", "并发", "多线程"]', 'session_003');
