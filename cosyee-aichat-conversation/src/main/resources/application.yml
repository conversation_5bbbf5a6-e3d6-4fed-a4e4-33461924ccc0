server:
  port: 9001

spring:
  application:
    name: cosyee-aichat-conversation
  
  # 数据库配置
  datasource:
    url: *****************************************************************************************************************************************************************************************************
    username: root
    password: mysql_jMdxCH
    driver-class-name: com.mysql.cj.jdbc.Driver

    # 连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000

  # Redis 配置
  redis:
    host: ************
    port: 6379
    password: Zz0.0.0.
    database: 0
    timeout: 5000ms

    # Lettuce 连接池配置
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms
  
# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.cosyee.conversation.entity
  
  # JSON 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: Cosyee AI Chat Conversation Service
    description: Spring Boot service for managing AI conversation records
    version: 1.0.0
  java:
    version: ${java.version}
  spring:
    version: ${spring-boot.version}

# 日志配置
logging:
  level:
    com.cosyee: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/cosyee-aichat-conversation.log

# 自定义配置
cosyee:
  conversation:
    # 分页配置
    page:
      default-size: 10
      max-size: 100
    # 搜索配置
    search:
      max-results: 50
      highlight-enabled: true
