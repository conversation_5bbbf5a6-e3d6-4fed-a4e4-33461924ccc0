# Cosyee Cloud 端口分配表

## 🌐 **服务端口分配**

| 服务名称 | 端口 | 状态 | 描述 | 访问地址 |
|---------|------|------|------|----------|
| **cosyee-gateway** | 9000 | ✅ 运行中 | Spring Cloud Gateway 网关服务 | http://localhost:9000 |
| **cosyee-aichat-conversation** | 9001 | ✅ 运行中 | AI对话记录服务 (MyBatis-Plus + MySQL) | http://localhost:9001 |
| **cosyee-mcp-server** | 9002 | 🔄 待创建 | Python MCP 服务器 | http://localhost:9002 |
| **cosyee-file-service** | 9003 | 🔄 待创建 | 文件管理服务 | http://localhost:9003 |
| **cosyee-user-service** | 9004 | 🔄 待创建 | 用户管理服务 | http://localhost:9004 |

## 🗄️ **数据库端口分配**

| 数据库类型 | 端口 | 地址 | 状态 | 描述 |
|-----------|------|------|------|------|
| **MySQL** | 3306 | ************ | ✅ 运行中 | 主数据库 (1Panel 安装) |
| **Redis** | 6379 | ************ | ✅ 运行中 | 缓存数据库 (密码: Zz0.0.0.) |

## 🔗 **Gateway 路由配置**

| 路径 | 目标服务 | 端口 | 描述 |
|------|----------|------|------|
| `/api/conversations/**` | cosyee-aichat-conversation | 9001 | 对话记录相关API |
| `/api/mcp/**` | cosyee-mcp-server | 9002 | MCP服务相关API |
| `/api/files/**` | cosyee-file-service | 9003 | 文件管理相关API |
| `/api/users/**` | cosyee-user-service | 9004 | 用户管理相关API |
| `/api/health` | 所有服务 | - | 健康检查 |

## 📊 **端口范围规划**

- **9000-9099**: 核心服务端口
- **9100-9199**: 扩展服务端口  
- **9200-9299**: 监控和管理服务端口
- **9300-9399**: 测试和开发服务端口

## 🔧 **配置更新记录**

### 2025-07-26 02:00
- ✅ Gateway 端口: 8080 → 9000
- ✅ Conversation 服务端口: 8081 → 9001
- ✅ 数据库连接: ************:3306
- ✅ MyBatis-Plus 配置完成

## 🚀 **服务启动命令**

```bash
# 启动 Gateway (端口 9000)
cd cosyee-gateway && mvn spring-boot:run

# 启动对话服务 (端口 9001)  
cd cosyee-aichat-conversation && mvn spring-boot:run

# 启动 MCP 服务器 (端口 9002)
cd cosyee-mcp-server && python main.py
```

## 🧪 **测试命令**

```bash
# 测试 Gateway
curl http://localhost:9000/

# 测试对话服务 (直接访问)
curl http://localhost:9001/api/health

# 测试对话服务 (通过 Gateway)
curl http://localhost:9000/api/health
```

## 📝 **注意事项**

1. **端口冲突检查**: 启动前请确保端口未被占用
2. **防火墙设置**: 确保端口在防火墙中开放
3. **服务依赖**: Gateway 依赖后端服务，请按顺序启动
4. **数据库连接**: 确保 MySQL 服务正常运行
5. **配置同步**: 修改端口后需要同步更新所有相关配置文件
